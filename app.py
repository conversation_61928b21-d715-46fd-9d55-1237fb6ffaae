from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os
import json

app = Flask(__name__)

# Disable caching during development
@app.after_request
def after_request(response):
    if app.debug:  # Only in development mode
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
    return response

# Database configuration - SQLite for local development, PostgreSQL for production
if os.environ.get('FLASK_ENV') == 'production':
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'postgresql://vxivdkko_lizfarm:LizFarm2024!@localhost/vxivdkko_lizfarm'
else:
    DATABASE_URL = 'sqlite:///lizfarm_local.db'

app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'liz-farm-production-secret-key-2024-secure'
app.config['SQLALCHEMY_DATABASE_URI'] = DATABASE_URL
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# WhatsApp configuration
WHATSAPP_NUMBER = os.environ.get('WHATSAPP_NUMBER') or "+254723812388"

# Database Models
class Contact(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20))
    subject = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'subject': self.subject,
            'message': self.message,
            'created_at': self.created_at.isoformat()
        }

class JobApplication(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    job_type = db.Column(db.String(50), nullable=False)  # local, international, training
    experience = db.Column(db.Text)
    message = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'job_type': self.job_type,
            'experience': self.experience,
            'message': self.message,
            'status': self.status,
            'created_at': self.created_at.isoformat()
        }

class TourBooking(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    tour_type = db.Column(db.String(50), nullable=False)
    preferred_date = db.Column(db.Date, nullable=False)
    group_size = db.Column(db.Integer, nullable=False)
    message = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'tour_type': self.tour_type,
            'preferred_date': self.preferred_date.isoformat(),
            'group_size': self.group_size,
            'message': self.message,
            'status': self.status,
            'created_at': self.created_at.isoformat()
        }

class NewsletterSubscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }

# In-memory storage for services and stats (in production, use a database)
stats_data = [
    {
        'id': 1,
        'title': 'Trained Farmers',
        'value': 500,
        'icon': 'fas fa-users',
        'color': 'farm-green'
    },
    {
        'id': 2,
        'title': 'Job Placements',
        'value': 250,
        'icon': 'fas fa-briefcase',
        'color': 'blue'
    },
    {
        'id': 3,
        'title': 'Countries Reached',
        'value': 8,
        'icon': 'fas fa-globe',
        'color': 'green'
    },
    {
        'id': 4,
        'title': 'Happy Visitors',
        'value': 1200,
        'icon': 'fas fa-heart',
        'color': 'red'
    }
]

testimonials_data = [
    {
        'id': 1,
        'name': 'Sarah Wanjiku',
        'role': 'Agricultural Training Graduate',
        'location': 'Nairobi, Kenya',
        'rating': 5,
        'text': 'Liz Farm completely transformed my understanding of modern agriculture. The training was comprehensive and practical. I now run my own successful farm and have increased my income by 300%!',
        'service': 'Agricultural Training',
        'date': '2024-01-15',
        'icon': 'fas fa-seedling',
        'color': 'farm-green'
    },
    {
        'id': 2,
        'name': 'James Mwangi',
        'role': 'Job Placement Success',
        'location': 'Dubai, UAE',
        'rating': 5,
        'text': 'Thanks to Liz Farm\'s job placement program, I secured an excellent position in Dubai\'s agricultural sector. Their training and support throughout the process was exceptional.',
        'service': 'Job Placement',
        'date': '2024-02-20',
        'icon': 'fas fa-briefcase',
        'color': 'blue'
    },
    {
        'id': 3,
        'name': 'Grace Akinyi',
        'role': 'Farm Tour Visitor',
        'location': 'Kisumu, Kenya',
        'rating': 5,
        'text': 'The AgriTour experience was eye-opening! My family learned so much about sustainable farming practices. The farm stay was comfortable and the activities were educational and fun.',
        'service': 'AgriTours & Farm Stays',
        'date': '2024-03-10',
        'icon': 'fas fa-camera',
        'color': 'green'
    },
    {
        'id': 4,
        'name': 'David Kiprop',
        'role': 'Recruited Farm Manager',
        'location': 'Eldoret, Kenya',
        'rating': 5,
        'text': 'Liz Farm\'s talent recruitment program connected me with the perfect farming opportunity. Their professional approach and extensive network helped me find my dream job in agriculture.',
        'service': 'Talent Recruitment',
        'date': '2024-01-30',
        'icon': 'fas fa-handshake',
        'color': 'purple'
    },
    {
        'id': 5,
        'name': 'Mary Njeri',
        'role': 'Training Program Graduate',
        'location': 'Meru, Kenya',
        'rating': 5,
        'text': 'The comprehensive agricultural training equipped me with both traditional knowledge and modern farming techniques. My crop yields have improved tremendously since completing the program.',
        'service': 'Agricultural Training',
        'date': '2023-12-05',
        'icon': 'fas fa-leaf',
        'color': 'farm-green'
    },
    {
        'id': 6,
        'name': 'Peter Ochieng',
        'role': 'International Job Placement',
        'location': 'Qatar',
        'rating': 5,
        'text': 'From local farming to international opportunities - Liz Farm made my career transition seamless. Their job placement services are thorough, professional, and they provide ongoing support.',
        'service': 'Job Placement',
        'date': '2024-02-14',
        'icon': 'fas fa-globe',
        'color': 'blue'
    }
]

services_data = [
    {
        'id': 1,
        'title': 'Agricultural Training',
        'subtitle': '🌱 Learn & Grow',
        'description': 'Whether you\'re a beginner or experienced farmer, our comprehensive training programs cover modern farming techniques, sustainable practices, and farm management.',
        'features': [
            'Modern farming methods',
            'Sustainable agriculture practices',
            'Farm management techniques',
            'Certification programs available'
        ],
        'certification': 'Certified Agricultural Training | Government Recognized',
        'price': 25000,
        'currency': 'KES',
        'duration': '2-6 months',
        'icon': 'fas fa-seedling',
        'color': 'farm-green',
        'button_text': 'Start Learning',
        'expandable_details': {
            'duration': '2-6 months',
            'schedule': 'Flexible learning',
            'certification': 'Government certified',
            'practical': '100% hands-on training'
        }
    },
    {
        'id': 2,
        'title': 'Job Placement',
        'subtitle': '💼 Career Opportunities',
        'description': 'Connect with employment opportunities locally and internationally. We help match skilled farmers with the right job opportunities.',
        'features': [
            'Local employment opportunities',
            'International job programs',
            'Training and certification support',
            'Speed learner prioritization'
        ],
        'certification': 'Certified Job Placement | Proven Success Rate',
        'price': 15000,
        'currency': 'KES',
        'duration': 'Ongoing',
        'icon': 'fas fa-briefcase',
        'color': 'blue',
        'button_text': 'Find Jobs',
        'has_dual_buttons': True,
        'dual_buttons': [
            {'text': 'Local Jobs', 'price': 15000},
            {'text': 'International', 'price': 35000}
        ]
    },
    {
        'id': 3,
        'title': 'AgriTours & Farm Stays',
        'subtitle': '🚜 Experience & Learn',
        'description': 'Experience authentic farm life with our guided tours and comfortable farm stays. Perfect for families, students, and anyone interested in agriculture.',
        'features': [
            'Guided farm tours',
            'Educational visits and workshops',
            'Family-friendly activities',
            'Resort-style accommodation'
        ],
        'certification': 'Tourism Board Certified | Safety Guaranteed',
        'price': 5000,
        'currency': 'KES',
        'duration': '1-7 days',
        'icon': 'fas fa-camera',
        'color': 'green',
        'button_text': 'Book Tour'
    },
    {
        'id': 4,
        'title': 'Talent Recruitment',
        'subtitle': '🤝 Connect & Recruit',
        'description': 'Looking for skilled agricultural professionals? We help connect employers with trained farmers, managers, and agricultural specialists.',
        'features': [
            'Professional farmer recruitment',
            'Trainer and specialist placement',
            'International recruitment services',
            'Skill assessment and matching'
        ],
        'certification': 'Professional Recruitment | Quality Assured',
        'price': 0,
        'currency': 'KES',
        'duration': 'Flexible',
        'icon': 'fas fa-handshake',
        'color': 'purple',
        'button_text': 'Recruit Talent'
    }
]

@app.route('/')
def index():
    return render_template('index.html', services=services_data, stats=stats_data, testimonials=testimonials_data, whatsapp_number=WHATSAPP_NUMBER)

@app.route('/services')
def services_page():
    return render_template('services.html', services=services_data, whatsapp_number=WHATSAPP_NUMBER)

@app.route('/api/stats')
def api_stats():
    return jsonify(stats_data)

# Admin routes
@app.route('/admin')
def admin_login():
    if 'admin_logged_in' in session:
        return redirect(url_for('admin_dashboard'))
    return render_template('admin/login.html')

@app.route('/admin/login', methods=['POST'])
def admin_login_post():
    username = request.form.get('username')
    password = request.form.get('password')

    # Simple authentication (in production, use proper authentication)
    if username == 'admin' and password == 'lizfarm2024':
        session['admin_logged_in'] = True
        flash('Successfully logged in!', 'success')
        return redirect(url_for('admin_dashboard'))
    else:
        flash('Invalid credentials!', 'error')
        return redirect(url_for('admin_login'))

@app.route('/admin/logout')
def admin_logout():
    session.pop('admin_logged_in', None)
    flash('Successfully logged out!', 'success')
    return redirect(url_for('admin_login'))

@app.route('/admin/dashboard')
def admin_dashboard():
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))
    return render_template('admin/dashboard.html', services=services_data, stats=stats_data, testimonials=testimonials_data, whatsapp_number=WHATSAPP_NUMBER)

@app.route('/admin/services')
def admin_services():
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))
    return render_template('admin/services.html', services=services_data)

@app.route('/admin/services/add', methods=['GET', 'POST'])
def admin_add_service():
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    if request.method == 'POST':
        new_service = {
            'id': max([s['id'] for s in services_data]) + 1 if services_data else 1,
            'title': request.form.get('title'),
            'subtitle': request.form.get('subtitle'),
            'description': request.form.get('description'),
            'features': request.form.get('features').split('\n'),
            'certification': request.form.get('certification'),
            'price': int(request.form.get('price', 0)),
            'currency': 'KES',
            'duration': request.form.get('duration'),
            'icon': request.form.get('icon'),
            'color': request.form.get('color'),
            'button_text': request.form.get('button_text')
        }
        services_data.append(new_service)
        flash('Service added successfully!', 'success')
        return redirect(url_for('admin_services'))

    return render_template('admin/add_service.html')

@app.route('/admin/services/edit/<int:service_id>', methods=['GET', 'POST'])
def admin_edit_service(service_id):
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    service = next((s for s in services_data if s['id'] == service_id), None)
    if not service:
        flash('Service not found!', 'error')
        return redirect(url_for('admin_services'))

    if request.method == 'POST':
        service['title'] = request.form.get('title')
        service['subtitle'] = request.form.get('subtitle')
        service['description'] = request.form.get('description')
        service['features'] = request.form.get('features').split('\n')
        service['certification'] = request.form.get('certification')
        service['price'] = int(request.form.get('price', 0))
        service['duration'] = request.form.get('duration')
        service['icon'] = request.form.get('icon')
        service['color'] = request.form.get('color')
        service['button_text'] = request.form.get('button_text')

        flash('Service updated successfully!', 'success')
        return redirect(url_for('admin_services'))

    return render_template('admin/edit_service.html', service=service)

@app.route('/admin/services/delete/<int:service_id>', methods=['POST'])
def admin_delete_service(service_id):
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    global services_data
    services_data = [s for s in services_data if s['id'] != service_id]
    flash('Service deleted successfully!', 'success')
    return redirect(url_for('admin_services'))

# Stats management routes
@app.route('/admin/stats')
def admin_stats():
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))
    return render_template('admin/stats.html', stats=stats_data)

@app.route('/admin/stats/edit/<int:stat_id>', methods=['GET', 'POST'])
def admin_edit_stat(stat_id):
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    stat = next((s for s in stats_data if s['id'] == stat_id), None)
    if not stat:
        flash('Stat not found!', 'error')
        return redirect(url_for('admin_stats'))

    if request.method == 'POST':
        stat['title'] = request.form.get('title')
        stat['value'] = int(request.form.get('value', 0))
        stat['icon'] = request.form.get('icon')
        stat['color'] = request.form.get('color')

        flash('Stat updated successfully!', 'success')
        return redirect(url_for('admin_stats'))

    return render_template('admin/edit_stat.html', stat=stat)

@app.route('/admin/settings', methods=['GET', 'POST'])
def admin_settings():
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    global WHATSAPP_NUMBER

    if request.method == 'POST':
        WHATSAPP_NUMBER = request.form.get('whatsapp_number', WHATSAPP_NUMBER)
        flash('Settings updated successfully!', 'success')
        return redirect(url_for('admin_settings'))

    return render_template('admin/settings.html', whatsapp_number=WHATSAPP_NUMBER)

@app.route('/admin/testimonials')
def admin_testimonials():
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))
    return render_template('admin/testimonials.html', testimonials=testimonials_data)

@app.route('/admin/testimonials/add', methods=['GET', 'POST'])
def admin_add_testimonial():
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    if request.method == 'POST':
        new_testimonial = {
            'id': max([t['id'] for t in testimonials_data]) + 1 if testimonials_data else 1,
            'name': request.form.get('name'),
            'role': request.form.get('role'),
            'location': request.form.get('location'),
            'icon': request.form.get('icon'),
            'color': request.form.get('color'),
            'rating': int(request.form.get('rating', 5)),
            'text': request.form.get('text'),
            'service': request.form.get('service'),
            'date': request.form.get('date')
        }
        testimonials_data.append(new_testimonial)
        flash('Testimonial added successfully!', 'success')
        return redirect(url_for('admin_testimonials'))

    return render_template('admin/add_testimonial.html', services=services_data)

@app.route('/admin/testimonials/edit/<int:testimonial_id>', methods=['GET', 'POST'])
def admin_edit_testimonial(testimonial_id):
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    testimonial = next((t for t in testimonials_data if t['id'] == testimonial_id), None)
    if not testimonial:
        flash('Testimonial not found!', 'error')
        return redirect(url_for('admin_testimonials'))

    if request.method == 'POST':
        testimonial['name'] = request.form.get('name')
        testimonial['role'] = request.form.get('role')
        testimonial['location'] = request.form.get('location')
        testimonial['icon'] = request.form.get('icon')
        testimonial['color'] = request.form.get('color')
        testimonial['rating'] = int(request.form.get('rating', 5))
        testimonial['text'] = request.form.get('text')
        testimonial['service'] = request.form.get('service')
        testimonial['date'] = request.form.get('date')

        flash('Testimonial updated successfully!', 'success')
        return redirect(url_for('admin_testimonials'))

    return render_template('admin/edit_testimonial.html', testimonial=testimonial, services=services_data)

@app.route('/admin/testimonials/delete/<int:testimonial_id>', methods=['POST'])
def admin_delete_testimonial(testimonial_id):
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))

    global testimonials_data
    testimonials_data = [t for t in testimonials_data if t['id'] != testimonial_id]
    flash('Testimonial deleted successfully!', 'success')
    return redirect(url_for('admin_testimonials'))

@app.route('/api/contact', methods=['POST'])
def contact():
    try:
        data = request.get_json()

        # Save to database
        contact = Contact(
            name=data.get('name'),
            email=data.get('email'),
            phone=data.get('phone'),
            subject=data.get('subject'),
            message=data.get('message')
        )
        db.session.add(contact)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Message sent successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/api/apply-job', methods=['POST'])
def apply_job():
    try:
        data = request.get_json()

        # Save to database
        application = JobApplication(
            name=data.get('name'),
            email=data.get('email'),
            phone=data.get('phone'),
            job_type=data.get('job_type'),
            experience=data.get('experience'),
            message=data.get('message')
        )
        db.session.add(application)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Application submitted successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/api/book-tour', methods=['POST'])
def book_tour():
    try:
        data = request.get_json()

        # Save to database
        booking = TourBooking(
            name=data.get('name'),
            email=data.get('email'),
            phone=data.get('phone'),
            tour_type=data.get('tour_type'),
            preferred_date=datetime.strptime(data.get('preferred_date'), '%Y-%m-%d').date(),
            group_size=int(data.get('group_size')),
            message=data.get('message')
        )
        db.session.add(booking)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Tour booked successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/api/subscribe', methods=['POST'])
def subscribe():
    try:
        data = request.get_json()

        # Check if email already exists
        existing = NewsletterSubscription.query.filter_by(email=data.get('email')).first()
        if existing:
            if existing.is_active:
                return jsonify({'success': False, 'message': 'Email already subscribed!'})
            else:
                existing.is_active = True
                db.session.commit()
                return jsonify({'success': True, 'message': 'Successfully resubscribed to newsletter!'})

        # Save to database
        subscription = NewsletterSubscription(email=data.get('email'))
        db.session.add(subscription)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Successfully subscribed to newsletter!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 400

# Initialize database
@app.before_first_request
def create_tables():
    """Create database tables if they don't exist"""
    try:
        db.create_all()
        print("✅ Database tables created successfully")
    except Exception as e:
        print(f"❌ Error creating database tables: {e}")

if __name__ == '__main__':
    # Create tables
    with app.app_context():
        db.create_all()
        print("✅ Database initialized")

    # Enable debug mode for development (disables caching)
    debug_mode = os.environ.get('FLASK_ENV') != 'production'
    app.run(debug=debug_mode, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
