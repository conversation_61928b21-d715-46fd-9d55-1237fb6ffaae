# 🚀 TrueHost Deployment - Quick Start Guide

## 📋 **DEPLOYMENT CHECKLIST**

### **✅ Step 1: Access TrueHost cPanel**
1. Login to your TrueHost account
2. Go to cPanel
3. Find **"File Manager"** in the Files section
4. Click **"File Manager"**

### **✅ Step 2: Create Project Folder**
1. Navigate to `public_html/`
2. Create new folder: **`lizfarm`**
3. Enter the `lizfarm/` folder

### **✅ Step 3: Upload These Files**
Upload to `public_html/lizfarm/`:

**📁 Main Files:**
- ✅ `app.py`
- ✅ `wsgi.py`
- ✅ `requirements.txt`
- ✅ `.htaccess`

**📁 Folders:**
- ✅ `templates/` (entire folder)
- ✅ `static/` (entire folder)

### **✅ Step 4: Set Up Python Application**
1. In cPanel, find **"Python App"** or **"Setup Python App"**
2. Click **"Create Application"**
3. Configure:
   - **Python Version**: `3.6+`
   - **Application Root**: `/public_html/lizfarm`
   - **Application URL**: Leave blank or `lizfarm`
   - **Application Startup File**: `wsgi.py`
   - **Application Entry Point**: `application`

### **✅ Step 5: Install Dependencies**
1. In Python App interface, click **"Open Terminal"**
2. Run command: `pip install -r requirements.txt`
3. Wait for installation to complete

### **✅ Step 6: Set File Permissions**
Set these permissions in File Manager:
- **Folders**: `755`
- **Python files**: `644`
- **`.htaccess`**: `644`

### **✅ Step 7: Test Your Website**
Visit: `https://astrabyte.africa/lizfarm`

## 🔧 **TROUBLESHOOTING**

### **❌ 500 Internal Server Error**
- Check file permissions
- Verify `wsgi.py` is correct
- Check error logs in cPanel

### **❌ Module Not Found Error**
- Ensure `requirements.txt` is uploaded
- Run `pip install -r requirements.txt` again
- Check Python version compatibility

### **❌ Template Not Found**
- Verify `templates/` folder is uploaded
- Check folder structure matches exactly

### **❌ Static Files Not Loading**
- Verify `static/` folder is uploaded
- Check `.htaccess` file is present

## 📞 **NEED HELP?**
Contact TrueHost support with:
1. Screenshot of error
2. File structure screenshot
3. Python app configuration details

## 🎉 **SUCCESS!**
Once deployed, your Liz Farm website will be live at:
**https://astrabyte.africa/lizfarm**
