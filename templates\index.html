<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>🌿 Liz Farm | The Oasis - Agricultural Training & Job Placement</title>
    <meta name="description" content="Liz Farm - The Oasis: Leading agricultural training provider offering farming education, job placement, farm tours, and talent recruitment services.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                        'serif': ['Playfair Display', 'serif'],
                    },
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        'tech-blue': {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        'tech-gray': {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'slide-down': 'slideDown 0.8s ease-out',
                        'slide-left': 'slideLeft 0.8s ease-out',
                        'slide-right': 'slideRight 0.8s ease-out',
                        'bounce-slow': 'bounce 2s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        slideDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        slideLeft: {
                            '0%': { opacity: '0', transform: 'translateX(30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' },
                        },
                        slideRight: {
                            '0%': { opacity: '0', transform: 'translateX(-30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(34, 197, 94, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(34, 197, 94, 0.8)' },
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        /* Ensure full width and prevent horizontal scroll */
        html, body {
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
        }

        * {
            box-sizing: border-box;
        }

        /* Prevent any element from causing horizontal overflow */
        .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl, .max-w-3xl {
            max-width: 100%;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        @media (min-width: 640px) {
            .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl, .max-w-3xl {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
        }

        .gradient-bg {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 30%, #166534 70%, #14532d 100%);
        }

        .hero-pattern {
            background-image:
                radial-gradient(circle at 20% 30%, rgba(255,255,255,0.15) 0%, transparent 60%),
                radial-gradient(circle at 80% 70%, rgba(34, 197, 94, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 40%),
                radial-gradient(circle at 90% 20%, rgba(34, 197, 94, 0.15) 0%, transparent 45%);
        }
        
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .parallax {
            transform: translateZ(0);
            will-change: transform;
        }
        
        .intersection-observer {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .intersection-observer.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Mobile optimizations */
        @media (max-width: 640px) {
            .hero-pattern {
                background-size: 200px 200px, 150px 150px, 100px 100px, 120px 120px;
            }

            .card-hover:hover {
                transform: translateY(-4px) scale(1.01);
            }

            .animate-float {
                animation-duration: 4s;
            }
        }

        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
            .animate-float,
            .animate-bounce-slow,
            .animate-pulse-slow {
                animation: none;
            }

            .card-hover {
                transition: none;
            }

            .intersection-observer {
                opacity: 1;
                transform: none;
            }
        }

        /* Additional animations for testimonials */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        .animate-slide-in-up {
            animation: slideInUp 0.6s ease-out forwards;
        }

        .animate-fade-in-scale {
            animation: fadeInScale 0.8s ease-out forwards;
        }

        .animate-shimmer {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        /* Testimonial specific animations */
        .testimonial-card:hover .animate-float {
            animation-duration: 1.5s;
        }

        .testimonial-card:hover {
            transform: translateY(-8px) scale(1.02);
        }
    </style>
</head>
<body class="font-sans bg-gray-50 w-full min-w-full" x-data="{
    mobileMenuOpen: false,
    currentSection: 'home',
    scrollY: 0,
    showToast: false,
    toastMessage: '',
    toastType: 'success',
    init() {
        this.observeIntersections();
        this.handleScroll();
    },
    observeIntersections() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, { threshold: 0.1 });
        
        document.querySelectorAll('.intersection-observer').forEach(el => {
            observer.observe(el);
        });
    },
    handleScroll() {
        window.addEventListener('scroll', () => {
            this.scrollY = window.scrollY;
        });
    }
}" @scroll.window="scrollY = window.pageYOffset">

    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 transition-all duration-300"
         :class="scrollY > 50 ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-transparent'">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3 sm:py-4">
                <!-- Logo -->
                <div class="flex items-center space-x-2 animate-fade-in">
                    <div class="w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center animate-float">
                        <i class="fas fa-seedling text-white text-xs sm:text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-base sm:text-lg font-bold text-gray-900" :class="scrollY > 50 ? 'text-gray-900' : 'text-white'">
                            Liz Farm
                        </h1>
                        <p class="text-xs text-gray-600 hidden sm:block" :class="scrollY > 50 ? 'text-gray-600' : 'text-gray-200'">
                            The Oasis
                        </p>
                    </div>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-3 lg:space-x-4">
                    <a href="#home" class="nav-link transition-colors duration-300 font-medium text-sm"
                       :class="scrollY > 50 ? 'text-gray-700 hover:text-farm-green-600' : 'text-white hover:text-farm-green-100'">
                        Home
                    </a>
                    <a href="#services" class="nav-link transition-colors duration-300 font-medium text-sm"
                       :class="scrollY > 50 ? 'text-gray-700 hover:text-farm-green-600' : 'text-white hover:text-farm-green-100'">
                        Services
                    </a>
                    <a href="#about" class="nav-link transition-colors duration-300 font-medium text-sm"
                       :class="scrollY > 50 ? 'text-gray-700 hover:text-farm-green-600' : 'text-white hover:text-farm-green-100'">
                        About
                    </a>
                    <a href="#job-info" class="nav-link transition-colors duration-300 font-medium text-sm"
                       :class="scrollY > 50 ? 'text-gray-700 hover:text-farm-green-600' : 'text-white hover:text-farm-green-100'">
                        Job Info
                    </a>
                    <a href="#contact" class="nav-link transition-colors duration-300 font-medium text-sm"
                       :class="scrollY > 50 ? 'text-gray-700 hover:text-farm-green-600' : 'text-white hover:text-farm-green-100'">
                        Contact
                    </a>
                    <button @click="document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })"
                            class="bg-farm-green-500 hover:bg-farm-green-600 text-white hover:text-white px-3 lg:px-4 py-2 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold ml-2 lg:ml-4 relative z-10 whitespace-nowrap text-sm">
                        Get Started
                    </button>
                </div>
                
                <!-- Mobile Menu Button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 rounded-lg transition-colors duration-300 relative z-50"
                        :class="scrollY > 50 ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white/10'">
                    <i class="fas fa-bars text-lg sm:text-xl" x-show="!mobileMenuOpen"></i>
                    <i class="fas fa-times text-lg sm:text-xl" x-show="mobileMenuOpen"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu Backdrop -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="mobileMenuOpen = false"
             class="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-30"></div>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-x-full"
             x-transition:enter-end="opacity-100 transform translate-x-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-x-0"
             x-transition:leave-end="opacity-0 transform -translate-x-full"
             class="md:hidden fixed inset-y-0 left-0 w-80 max-w-sm bg-gradient-to-b from-tech-blue-600 to-tech-blue-700 shadow-2xl z-40"
             style="padding-top: 20px;">
            <div class="px-4 py-4 space-y-4 h-full">
                <!-- Logo inside mobile menu at the top -->
                <div class="flex items-center space-x-2 mb-6 pb-3 border-b border-white/20">
                    <div class="w-8 h-8 bg-gradient-to-br from-farm-green-400 to-farm-green-300 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-sm font-bold text-white">Liz Farm</h1>
                        <p class="text-xs text-tech-blue-100">The Oasis</p>
                    </div>
                </div>
                <a href="#home" class="block text-sm font-medium text-white hover:text-tech-blue-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-home mr-2 text-tech-blue-100 text-xs"></i>Home
                </a>
                <a href="#services" class="block text-sm font-medium text-white hover:text-tech-blue-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-seedling mr-2 text-tech-blue-100 text-xs"></i>Services
                </a>
                <a href="#about" class="block text-sm font-medium text-white hover:text-tech-blue-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-info-circle mr-2 text-tech-blue-100 text-xs"></i>About
                </a>
                <a href="#job-info" class="block text-sm font-medium text-white hover:text-tech-blue-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-briefcase mr-2 text-tech-blue-100 text-xs"></i>Job Info
                </a>
                <a href="#contact" class="block text-sm font-medium text-white hover:text-tech-blue-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-envelope mr-2 text-tech-blue-100 text-xs"></i>Contact
                </a>
                <button @click="mobileMenuOpen = false; document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })"
                        class="w-full bg-white text-tech-blue-700 hover:bg-tech-blue-50 hover:text-tech-blue-800 px-3 py-2 rounded-full transition-all duration-300 font-semibold text-sm shadow-lg mt-4 transform hover:scale-105">
                    <i class="fas fa-rocket mr-2"></i>Get Started
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-farm-green-600 via-farm-green-700 to-farm-green-800 hero-pattern w-full">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-20 h-20 bg-white/20 rounded-full animate-float shadow-lg" style="animation-delay: 0s;"></div>
            <div class="absolute top-40 right-20 w-16 h-16 bg-tech-blue-300/30 rounded-full animate-float shadow-lg" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-40 left-20 w-24 h-24 bg-white/15 rounded-full animate-float shadow-lg" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-20 right-10 w-12 h-12 bg-tech-blue-400/40 rounded-full animate-float shadow-lg" style="animation-delay: 0.5s;"></div>
            <div class="absolute top-1/2 left-1/4 w-8 h-8 bg-yellow-300/30 rounded-full animate-float shadow-lg" style="animation-delay: 1.5s;"></div>
            <div class="absolute top-1/3 right-1/3 w-14 h-14 bg-white/10 rounded-full animate-float shadow-lg" style="animation-delay: 2.5s;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="animate-slide-down">
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-4 sm:mb-6 font-serif drop-shadow-lg leading-tight">
                    🌿 Liz Farm
                    <span class="block text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl mt-2 text-green-100 drop-shadow-md">The Oasis</span>
                </h1>
                <p class="text-lg sm:text-xl md:text-2xl text-white mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed drop-shadow-md font-medium px-2">
                    Where Agriculture, Opportunity & Success Converge
                </p>
                <p class="text-sm sm:text-base md:text-lg text-green-50 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed drop-shadow-sm px-4">
                    Leading agricultural transformation with modern farming techniques and global opportunities. From training to job placement,
                    we deliver comprehensive solutions that drive agricultural growth and career excellence.
                </p>
            </div>

            <div class="animate-slide-up flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4" style="animation-delay: 0.3s;">
                <button @click="document.getElementById('services').scrollIntoView({ behavior: 'smooth' })"
                        class="w-full sm:w-auto bg-white text-farm-green-700 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg hover:bg-farm-green-50 hover:text-farm-green-800 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                    <i class="fas fa-seedling mr-2"></i>
                    Explore Our Services
                </button>
                <button @click="document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })"
                        class="w-full sm:w-auto border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg hover:bg-white hover:text-farm-green-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    <i class="fas fa-phone mr-2"></i>
                    Contact Us
                </button>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div @click="document.getElementById('services').scrollIntoView({ behavior: 'smooth' })"
             class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-slow cursor-pointer hover:scale-110 transition-transform duration-300">
            <div class="w-6 h-10 border-2 border-white/70 rounded-full flex justify-center shadow-lg hover:border-white">
                <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-12 sm:py-16 md:py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Debug: Check if stats data exists -->
            {% if not stats %}
            <div class="text-center text-red-600 mb-8">
                <p>No stats data found. Please check admin configuration.</p>
            </div>
            {% endif %}

            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
                {% for stat in stats %}
                <div class="text-center intersection-observer p-4">
                    <div class="flex items-center justify-center mb-3">
                        <div class="w-12 h-12 bg-{{ stat.color }}-100 rounded-full flex items-center justify-center">
                            <i class="{{ stat.icon }} text-{{ stat.color }}-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-{{ stat.color }}-600 mb-2"
                         x-data="{
                             count: 0,
                             started: false,
                             target: {{ stat.value }},
                             init() {
                                 // Start animation when component initializes and is visible
                                 this.$nextTick(() => {
                                     setTimeout(() => {
                                         this.startAnimation();
                                     }, 500);
                                 });
                             },
                             startAnimation() {
                                 if (this.started || this.target === 0) return;
                                 this.started = true;
                                 const increment = Math.max(1, Math.ceil(this.target / 50));
                                 const interval = setInterval(() => {
                                     if(this.count < this.target) {
                                         this.count = Math.min(this.count + increment, this.target);
                                     } else {
                                         clearInterval(interval);
                                     }
                                 }, 50);
                             }
                         }">
                        <span x-text="count || {{ stat.value }}"></span>+
                    </div>
                    <p class="text-sm sm:text-base text-gray-600 font-medium">{{ stat.title }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-12 sm:py-16 md:py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16 intersection-observer">
                <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-serif px-4">
                    🌾 What We Offer
                </h2>
                <p class="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                    Comprehensive programs designed to empower individuals and communities through sustainable agriculture,
                    global opportunities, and transformative experiences.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-6 sm:gap-8 mb-12 sm:mb-16">
                {% for service in services %}
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg card-hover intersection-observer"
                     x-data="{ expanded: false }">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center mb-6">
                        <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-{{ service.color }}-400 to-{{ service.color }}-600 rounded-full flex items-center justify-center mb-4 sm:mb-0 sm:mr-4 animate-float">
                            <i class="{{ service.icon }} text-white text-lg sm:text-2xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 mb-1">{{ service.title }}</h3>
                            <p class="text-sm sm:text-base text-{{ service.color }}-600 font-medium">{{ service.subtitle }}</p>
                        </div>
                        <div class="text-right mt-4 sm:mt-0">
                            <p class="text-lg sm:text-xl font-bold text-gray-900">
                                {% if service.price > 0 %}
                                    KES {{ "{:,}".format(service.price) }}
                                {% else %}
                                    Free
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-600">{{ service.duration }}</p>
                        </div>
                    </div>

                    <p class="text-sm sm:text-base text-gray-600 mb-6">
                        {{ service.description }}
                    </p>

                    <div class="space-y-3 mb-6">
                        {% for feature in service.features %}
                        <div class="flex items-center text-sm sm:text-base text-gray-700">
                            <i class="fas fa-check-circle text-{{ service.color }}-500 mr-3 flex-shrink-0"></i>
                            <span>{{ feature }}</span>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="bg-{{ service.color }}-50 rounded-lg p-3 sm:p-4 mb-4">
                        <p class="text-sm sm:text-base text-{{ service.color }}-800 font-medium">
                            <i class="fas fa-certificate mr-2"></i>
                            {{ service.certification }}
                        </p>
                    </div>

                    {% if service.expandable_details %}
                    <!-- Expandable Details -->
                    <div x-show="expanded" x-transition class="mb-4 p-4 bg-gray-50 rounded-lg border-l-4 border-{{ service.color }}-500">
                        <h4 class="font-semibold text-gray-800 mb-3">Program Details:</h4>
                        <div class="grid sm:grid-cols-2 gap-3 text-sm text-gray-700">
                            {% for key, value in service.expandable_details.items() %}
                            <div class="flex items-center">
                                <i class="fas fa-{% if key == 'duration' %}clock{% elif key == 'schedule' %}calendar{% elif key == 'certification' %}award{% else %}hands-helping{% endif %} text-{{ service.color }}-500 mr-2"></i>
                                <span>{{ key.title() }}: {{ value }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="flex gap-3 mb-4">
                        <button @click="expanded = !expanded"
                                class="text-{{ service.color }}-600 hover:text-{{ service.color }}-700 text-sm font-medium transition-all duration-300 flex items-center">
                            <span x-text="expanded ? 'Show Less' : 'Learn More'"></span>
                            <i class="fas fa-chevron-down ml-1 transition-transform duration-300" :class="expanded ? 'rotate-180' : ''"></i>
                        </button>
                    </div>
                    {% endif %}

                    <div class="flex gap-3">
                        {% if service.has_dual_buttons %}
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 flex-1">
                            {% for button in service.dual_buttons %}
                            <button @click="scrollToContactWithMessage('I am interested in {{ button.text.lower() }} opportunities. Please provide information about available positions and requirements.')"
                                    class="bg-{{ service.color }}-500 hover:bg-{{ service.color }}-600 text-white py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 transform hover:scale-105">
                                {{ button.text }}
                                <span class="block text-xs mt-1">KES {{ "{:,}".format(button.price) }}</span>
                            </button>
                            {% endfor %}
                        </div>
                        {% else %}
                        <button @click="scrollToContactWithMessage('I am interested in {{ service.title }}. Please provide more information about enrollment, pricing, and requirements.')"
                                class="flex-1 bg-{{ service.color }}-500 hover:bg-{{ service.color }}-600 text-white py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-arrow-right mr-2"></i>
                            {{ service.button_text }}
                        </button>
                        {% endif %}

                        <!-- WhatsApp Button -->
                        <button onclick="openWhatsApp('{{ service.title }}', 'I am interested in {{ service.title }}. Please provide more information about enrollment, pricing, and requirements.')"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                            <i class="fab fa-whatsapp text-xl"></i>
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- View All Services Button -->
            <div class="text-center">
                <a href="/services" class="inline-flex items-center bg-farm-green-500 hover:bg-farm-green-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-th-large mr-3"></i>
                    View All Services
                    <i class="fas fa-arrow-right ml-3"></i>
                </a>
                <p class="text-gray-600 mt-4">Explore detailed information about all our services and offerings</p>
            </div>
        </div>
    </section>

    <!-- Job Placement & Training Information Section -->
    <section id="job-info" class="py-12 sm:py-16 md:py-20 bg-gradient-to-br from-farm-green-50 to-farm-green-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16 intersection-observer">
                <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-serif px-4">
                    🌍 Job Placement & Training Information
                </h2>
                <p class="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                    Comprehensive training programs and job placement services to launch your agricultural career
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
                <!-- Registration Fees -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg intersection-observer">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-farm-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-user-plus text-2xl text-farm-green-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">💳 Registration Fees</h3>
                        <p class="text-gray-600 text-sm">Mandatory for All Applicants</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-farm-green-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-gray-900">Local Jobs</span>
                                <span class="text-2xl font-bold text-farm-green-600">$15 USD</span>
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-gray-900">International Jobs</span>
                                <span class="text-2xl font-bold text-blue-600">$50 USD</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                        <p class="text-sm text-yellow-800 mb-3">
                            <i class="fas fa-info-circle mr-2"></i>
                            Registration is required before proceeding to training, interviews, or job placement
                        </p>
                        <button onclick="openWhatsApp('Registration Fee Inquiry', 'I would like to register and pay the registration fee')"
                                class="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 flex items-center justify-center">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Pay Registration Fee
                        </button>
                    </div>
                </div>

                <!-- Training Categories -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg intersection-observer">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-graduation-cap text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">🎓 Training Categories</h3>
                        <p class="text-gray-600 text-sm">Choose Your Path</p>
                    </div>

                    <div class="space-y-6">
                        <div class="border-l-4 border-farm-green-500 pl-4">
                            <h4 class="font-bold text-gray-900 mb-2">1. Already Trained / With Experience</h4>
                            <ul class="text-sm text-gray-600 space-y-1 mb-4">
                                <li>• Eligible for free refresher courses</li>
                                <li>• Interview and job placement preparation</li>
                                <li>• Duration: A few days</li>
                                <li>• Local or international opportunities</li>
                            </ul>
                        </div>

                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-bold text-gray-900 mb-2">2. Untrained / Without Experience</h4>
                            <ul class="text-sm text-gray-600 space-y-1 mb-4">
                                <li>• Full training and job preparation required</li>
                                <li>• Skills training and certification</li>
                                <li>• Job connection services</li>
                                <li>• Comprehensive career support</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <button onclick="openWhatsApp('Experienced Applicant', 'I have experience in agriculture and would like to apply for refresher training')"
                                class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 flex items-center justify-center">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Apply (Experienced)
                        </button>
                        <button onclick="openWhatsApp('New Applicant', 'I am new to agriculture and would like to apply for full training')"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 flex items-center justify-center">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Apply (New)
                        </button>
                    </div>
                </div>

                <!-- Training & Placement Fees -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg intersection-observer">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-dollar-sign text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">💰 Training & Placement Fees</h3>
                        <p class="text-gray-600 text-sm">Investment in Your Future</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-farm-green-50 rounded-lg p-4">
                            <div class="text-center">
                                <span class="block font-semibold text-gray-900 mb-1">Local Jobs</span>
                                <span class="text-2xl font-bold text-farm-green-600">From $200 USD</span>
                                <span class="text-sm text-gray-600">(≈ KES 26,000)</span>
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-center">
                                <span class="block font-semibold text-gray-900 mb-1">International Jobs</span>
                                <span class="text-2xl font-bold text-blue-600">From $500 USD</span>
                                <span class="text-sm text-gray-600">(≈ KES 65,000)</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-green-50 rounded-lg border-l-4 border-green-400">
                        <p class="text-sm text-green-800 mb-3">
                            <i class="fas fa-check-circle mr-2"></i>
                            Includes comprehensive training, certification, and job placement support
                        </p>
                        <button onclick="openWhatsApp('Training Fees Inquiry', 'I would like to know more about training fees and payment options')"
                                class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 flex items-center justify-center">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Ask About Training Fees
                        </button>
                    </div>
                </div>
            </div>

            <!-- Important Note -->
            <div class="bg-gradient-to-r from-farm-green-600 to-farm-green-700 rounded-2xl p-6 sm:p-8 text-white text-center intersection-observer">
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4">✅ Important Note</h3>
                    <p class="text-lg leading-relaxed">
                        All applicants must first register by paying the registration fee before proceeding to training,
                        interviews, or job connection services. This ensures serious commitment and helps us provide
                        personalized career guidance.
                    </p>
                    <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="openWhatsApp('Registration Inquiry', 'I would like to register for job placement services')"
                                class="bg-white text-farm-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Register Now
                        </button>
                        <button onclick="openWhatsApp('Training Inquiry', 'I need more information about training programs')"
                                class="bg-farm-green-500 hover:bg-farm-green-400 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-info-circle mr-2"></i>
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 intersection-observer">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4 font-serif">
                    📍 Why Choose Liz Farm – The Oasis?
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Discover what makes us the premier destination for agricultural training,
                    job placement, and sustainable farming experiences in Kenya.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="text-center intersection-observer card-hover bg-gray-50 rounded-2xl p-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center mx-auto mb-6 animate-float">
                        <i class="fas fa-map-marker-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Prime Location</h3>
                    <p class="text-gray-600">
                        Located in scenic Jaika Hill Estate, offering breathtaking natural beauty
                        and ideal conditions for sustainable agriculture.
                    </p>
                </div>

                <div class="text-center intersection-observer card-hover bg-gray-50 rounded-2xl p-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 animate-float" style="animation-delay: 0.2s;">
                        <i class="fas fa-network-wired text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Trusted Network</h3>
                    <p class="text-gray-600">
                        Established network across Kenya and beyond, connecting you with
                        opportunities locally and internationally.
                    </p>
                </div>

                <div class="text-center intersection-observer card-hover bg-gray-50 rounded-2xl p-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 animate-float" style="animation-delay: 0.4s;">
                        <i class="fas fa-cogs text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Structured Economy</h3>
                    <p class="text-gray-600">
                        Fully structured internal economy designed to maximize opportunities
                        and sustainable growth for all participants.
                    </p>
                </div>

                <div class="text-center intersection-observer card-hover bg-gray-50 rounded-2xl p-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 animate-float" style="animation-delay: 0.6s;">
                        <i class="fas fa-user-graduate text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Expert Leadership</h3>
                    <p class="text-gray-600">
                        Run by experienced entrepreneurs with deep knowledge in agriculture,
                        business development, and international markets.
                    </p>
                </div>

                <div class="text-center intersection-observer card-hover bg-gray-50 rounded-2xl p-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 animate-float" style="animation-delay: 0.8s;">
                        <i class="fas fa-robot text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">AI-Enabled Systems</h3>
                    <p class="text-gray-600">
                        Powered by cutting-edge AI technology to optimize training programs,
                        job matching, and operational efficiency.
                    </p>
                </div>

                <div class="text-center intersection-observer card-hover bg-gray-50 rounded-2xl p-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6 animate-float" style="animation-delay: 1s;">
                        <i class="fas fa-heart text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Mission-Driven</h3>
                    <p class="text-gray-600">
                        Committed to building Africa's future through sustainable practices,
                        education, and empowerment of local communities.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-12 sm:py-16 md:py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16 intersection-observer">
                <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-serif px-4">
                    💬 What Our Community Says
                </h2>
                <p class="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                    Real stories from real people who have transformed their lives through our programs.
                    Join thousands of satisfied participants who have achieved their dreams with Liz Farm.
                </p>
            </div>

            <!-- Testimonials Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12">
                {% for testimonial in testimonials %}
                <div class="relative bg-white rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-500 intersection-observer card-hover transform hover:scale-105"
                     style="animation-delay: {{ loop.index0 * 0.2 }}s;">

                    <!-- Service Icon with Animation -->
                    <div class="flex items-center justify-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-{{ testimonial.color }}-400 to-{{ testimonial.color }}-600 rounded-full flex items-center justify-center animate-float shadow-lg">
                            <i class="{{ testimonial.icon }} text-white text-2xl"></i>
                        </div>
                    </div>

                    <!-- Rating Stars with Stagger Animation -->
                    <div class="flex items-center justify-center mb-6">
                        {% for i in range(testimonial.rating) %}
                        <i class="fas fa-star text-yellow-400 text-lg mr-1 animate-pulse" style="animation-delay: {{ i * 0.1 }}s;"></i>
                        {% endfor %}
                        <span class="text-sm text-gray-500 ml-2">({{ testimonial.rating }}/5)</span>
                    </div>

                    <!-- Testimonial Text -->
                    <blockquote class="text-gray-700 mb-6 leading-relaxed text-center italic">
                        "{{ testimonial.text }}"
                    </blockquote>

                    <!-- Author Info with Icon -->
                    <div class="text-center mb-4">
                        <div class="w-12 h-12 bg-{{ testimonial.color }}-100 rounded-full flex items-center justify-center mx-auto mb-3 animate-bounce">
                            <i class="fas fa-user text-{{ testimonial.color }}-600 text-lg"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900">{{ testimonial.name }}</h4>
                        <p class="text-sm text-gray-600">{{ testimonial.role }}</p>
                        <p class="text-xs text-gray-500 flex items-center justify-center mt-1">
                            <i class="fas fa-map-marker-alt mr-1 text-{{ testimonial.color }}-500"></i>
                            {{ testimonial.location }}
                        </p>
                    </div>

                    <!-- Service Badge with Pulse -->
                    <div class="text-center">
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-{{ testimonial.color }}-100 text-{{ testimonial.color }}-800 animate-pulse">
                            <i class="fas fa-check-circle mr-2"></i>
                            {{ testimonial.service }}
                        </span>
                    </div>

                    <!-- Decorative Quote Marks -->
                    <div class="absolute top-4 left-4 text-{{ testimonial.color }}-200 text-4xl opacity-50">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <div class="absolute bottom-4 right-4 text-{{ testimonial.color }}-200 text-4xl opacity-50 transform rotate-180">
                        <i class="fas fa-quote-left"></i>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Call to Action -->
            <div class="text-center bg-white rounded-2xl p-8 shadow-lg intersection-observer">
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Join Our Success Stories?</h3>
                <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
                    Don't just take our word for it. Experience the transformation yourself and become part of our growing community of success stories.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#contact" class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-rocket mr-2"></i>
                        Start Your Journey
                    </a>
                    <button onclick="openWhatsApp('Testimonial Inquiry', 'I saw the testimonials and would like to learn more about your programs')"
                            class="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fab fa-whatsapp mr-2"></i>
                        Chat With Us
                    </button>
                </div>
            </div>
        </div>
    </section>



    <!-- Contact Section -->
    <section id="contact" class="py-12 sm:py-16 md:py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16 intersection-observer">
                <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-serif px-4">
                    📝 Get Started Today
                </h2>
                <p class="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                    Ready to transform your future? Apply for training, job placement, book your tour,
                    or partner with our mission-driven ecosystem.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8 sm:gap-12">
                <!-- Contact Form -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg intersection-observer"
                     x-data="contactForm()">
                    <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>

                    <form @submit.prevent="submitForm()" class="space-y-4 sm:space-y-6">
                        <div class="grid sm:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                                <input type="text" x-model="form.name" required
                                       class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-transparent transition-all duration-300">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                <input type="email" x-model="form.email" required
                                       class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-transparent transition-all duration-300">
                            </div>
                        </div>

                        <div class="grid sm:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input type="tel" x-model="form.phone"
                                       class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-transparent transition-all duration-300">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                                <select x-model="form.subject" required
                                        class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-transparent transition-all duration-300">
                                    <option value="">Select a subject</option>
                                    <option value="Agricultural Training">Agricultural Training</option>
                                    <option value="Job Application">Job Application</option>
                                    <option value="Tour Booking">Tour Booking</option>
                                    <option value="Partnership">Partnership</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                            <textarea x-model="form.message" required rows="4"
                                      class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-transparent transition-all duration-300 resize-none"
                                      placeholder="Tell us about your interests and goals..."></textarea>
                        </div>

                        <button type="submit" :disabled="loading"
                                class="w-full bg-farm-green-500 hover:bg-farm-green-600 disabled:bg-gray-400 text-white py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 transform hover:scale-105"
                                :class="loading ? 'cursor-not-allowed' : ''">
                            <span x-show="!loading">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Message
                            </span>
                            <span x-show="loading">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Sending...
                            </span>
                        </button>

                        <div x-show="message"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             class="p-4 rounded-lg"
                             :class="success ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'">
                            <span x-text="message"></span>
                        </div>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="space-y-8 intersection-observer">
                    <div class="bg-white rounded-2xl p-4 sm:p-8 shadow-lg">
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>

                        <div class="space-y-4 sm:space-y-6">
                            <div class="flex items-center">
                                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-farm-green-100 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                                    <i class="fas fa-envelope text-farm-green-600 text-sm sm:text-base"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="font-medium text-gray-900 text-sm sm:text-base">Email</p>
                                    <p class="text-gray-600 text-sm sm:text-base break-all"><EMAIL></p>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-farm-green-100 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                                    <i class="fas fa-phone text-farm-green-600 text-sm sm:text-base"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="font-medium text-gray-900 text-sm sm:text-base">Phone</p>
                                    <p class="text-gray-600 text-sm sm:text-base">+254 723 812388</p>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-farm-green-100 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                                    <i class="fas fa-map-marker-alt text-farm-green-600 text-sm sm:text-base"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="font-medium text-gray-900 text-sm sm:text-base">Location</p>
                                    <p class="text-gray-600 text-sm sm:text-base">Jaika Hill Estate, Kenya</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Newsletter Signup -->
                    <div class="bg-gradient-to-br from-farm-green-500 to-farm-green-600 rounded-2xl p-4 sm:p-8 text-white">
                        <h3 class="text-xl sm:text-2xl font-bold mb-4">Stay Updated</h3>
                        <p class="mb-6 text-farm-green-100 text-sm sm:text-base">
                            Subscribe to our newsletter for the latest updates on training programs,
                            job opportunities, and farm activities.
                        </p>

                        <div x-data="newsletterForm()" class="space-y-4">
                            <div class="flex flex-col sm:flex-row gap-3">
                                <input type="email" x-model="email" placeholder="Enter your email"
                                       class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:ring-2 focus:ring-white focus:outline-none min-w-0">
                                <button @click="subscribe()" :disabled="loading"
                                        class="bg-white text-farm-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 disabled:bg-gray-300 whitespace-nowrap">
                                    <span x-show="!loading">Subscribe</span>
                                    <span x-show="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </button>
                            </div>

                            <div x-show="message"
                                 x-transition:enter="transition ease-out duration-300"
                                 x-transition:enter-start="opacity-0"
                                 x-transition:enter-end="opacity-100"
                                 class="text-sm"
                                 :class="success ? 'text-green-100' : 'text-red-200'">
                                <span x-text="message"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 sm:py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-8 sm:mb-12">
                <!-- Company Info -->
                <div class="sm:col-span-2 lg:col-span-2">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-3 mb-6">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center animate-float">
                            <i class="fas fa-seedling text-white text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl sm:text-2xl font-bold">Liz Farm</h3>
                            <p class="text-sm sm:text-base text-gray-400">Where Agriculture, Opportunity & Success Converge</p>
                        </div>
                    </div>
                    <p class="text-sm sm:text-base text-gray-300 mb-6 max-w-md">
                        Building an ecosystem of empowerment through sustainable agriculture,
                        global job placements, and transformative experiences in Jaika Hill Estate, Kenya.
                    </p>
                    <div class="flex space-x-3 sm:space-x-4">
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-facebook-f text-sm sm:text-base"></i>
                        </a>
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-twitter text-sm sm:text-base"></i>
                        </a>
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-instagram text-sm sm:text-base"></i>
                        </a>
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-linkedin-in text-sm sm:text-base"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-base sm:text-lg font-semibold mb-4 sm:mb-6">Quick Links</h4>
                    <ul class="space-y-2 sm:space-y-3">
                        <li><a href="#home" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Home</a></li>
                        <li><a href="#services" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Services</a></li>
                        <li><a href="#about" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">About</a></li>
                        <li><a href="#job-info" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Job Info</a></li>
                        <li><a href="#contact" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Contact</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div>
                    <h4 class="text-base sm:text-lg font-semibold mb-4 sm:mb-6">Our Services</h4>
                    <ul class="space-y-2 sm:space-y-3">
                        <li><a href="#" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Agricultural Training</a></li>
                        <li><a href="#" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Job Placement</a></li>
                        <li><a href="#" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">AgriTours</a></li>
                        <li><a href="#" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Talent Recruitment</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 pt-6 sm:pt-8 text-center">
                <p class="text-xs sm:text-sm text-gray-400 px-4">
                    © 2024 Liz Farm. All rights reserved. |
                    <a href="#" class="hover:text-tech-blue-400 transition-colors duration-300">Privacy Policy</a> |
                    <a href="#" class="hover:text-tech-blue-400 transition-colors duration-300">Terms of Service</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Toast Notification -->
    <div x-show="showToast"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-x-full"
         x-transition:enter-end="opacity-100 transform translate-x-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-x-0"
         x-transition:leave-end="opacity-0 transform translate-x-full"
         class="fixed top-20 right-6 max-w-sm bg-white rounded-lg shadow-lg border-l-4 p-4 z-50"
         :class="toastType === 'success' ? 'border-green-500' : 'border-red-500'">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas text-lg"
                   :class="toastType === 'success' ? 'fa-check-circle text-green-500' : 'fa-exclamation-circle text-red-500'"></i>
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium text-gray-900" x-text="toastMessage"></p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button @click="showToast = false" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button @click="window.scrollTo({ top: 0, behavior: 'smooth' })"
            x-show="scrollY > 500"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-95"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-95"
            class="fixed bottom-6 right-6 bg-farm-green-500 hover:bg-farm-green-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 z-50">
        <i class="fas fa-arrow-up text-lg"></i>
    </button>

    <!-- JavaScript -->
    <script>
        // Show toast notification
        function showToast(message, type = 'success') {
            const body = document.querySelector('body');
            const alpineData = Alpine.$data(body);

            alpineData.toastMessage = message;
            alpineData.toastType = type;
            alpineData.showToast = true;

            // Auto hide after 4 seconds
            setTimeout(() => {
                alpineData.showToast = false;
            }, 4000);
        }

        // Scroll to contact section with pre-filled message
        function scrollToContactWithMessage(message) {
            // Show confirmation toast
            showToast('Redirecting to contact form...', 'success');

            // Scroll to contact section
            document.getElementById('contact').scrollIntoView({ behavior: 'smooth' });

            // Wait for scroll to complete, then fill the message
            setTimeout(() => {
                const messageTextarea = document.querySelector('textarea[x-model="form.message"]');
                if (messageTextarea) {
                    // Trigger Alpine.js to update the model
                    messageTextarea.value = message;
                    messageTextarea.dispatchEvent(new Event('input'));
                    messageTextarea.focus();

                    // Show success message
                    setTimeout(() => {
                        showToast('Message pre-filled! Please complete the form.', 'success');
                    }, 500);
                }
            }, 800);
        }

        // Contact Form Handler
        function contactForm() {
            return {
                form: {
                    name: '',
                    email: '',
                    phone: '',
                    subject: '',
                    message: ''
                },
                loading: false,
                message: '',
                success: false,

                async submitForm() {
                    this.loading = true;
                    this.message = '';

                    try {
                        const response = await fetch('/api/contact', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(this.form)
                        });

                        const data = await response.json();

                        if (data.success) {
                            this.success = true;
                            this.message = data.message;
                            this.resetForm();
                        } else {
                            this.success = false;
                            this.message = data.message;
                        }
                    } catch (error) {
                        this.success = false;
                        this.message = 'An error occurred. Please try again.';
                    } finally {
                        this.loading = false;
                        setTimeout(() => {
                            this.message = '';
                        }, 5000);
                    }
                },

                resetForm() {
                    this.form = {
                        name: '',
                        email: '',
                        phone: '',
                        subject: '',
                        message: ''
                    };
                }
            }
        }

        // Newsletter Form Handler
        function newsletterForm() {
            return {
                email: '',
                loading: false,
                message: '',
                success: false,

                async subscribe() {
                    if (!this.email) {
                        this.success = false;
                        this.message = 'Please enter your email address.';
                        return;
                    }

                    this.loading = true;
                    this.message = '';

                    try {
                        const response = await fetch('/api/subscribe', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ email: this.email })
                        });

                        const data = await response.json();

                        if (data.success) {
                            this.success = true;
                            this.message = data.message;
                            this.email = '';
                        } else {
                            this.success = false;
                            this.message = data.message;
                        }
                    } catch (error) {
                        this.success = false;
                        this.message = 'An error occurred. Please try again.';
                    } finally {
                        this.loading = false;
                        setTimeout(() => {
                            this.message = '';
                        }, 5000);
                    }
                }
            }
        }

        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);

                    if (targetSection) {
                        const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar

                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.parallax');

            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Add loading animation to buttons
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');

            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.disabled) {
                        this.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = '';
                        }, 150);
                    }
                });
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to close mobile menu
            if (e.key === 'Escape') {
                const body = document.querySelector('body');
                const alpineData = Alpine.$data(body);
                if (alpineData.mobileMenuOpen) {
                    alpineData.mobileMenuOpen = false;
                }
                if (alpineData.showToast) {
                    alpineData.showToast = false;
                }
            }

            // Ctrl/Cmd + K to focus search (if we had one)
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('contact').scrollIntoView({ behavior: 'smooth' });
                setTimeout(() => {
                    const nameInput = document.querySelector('input[x-model="form.name"]');
                    if (nameInput) nameInput.focus();
                }, 800);
            }

            // Home key to go to top
            if (e.key === 'Home' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            // End key to go to bottom
            if (e.key === 'End' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            }
        });

        // WhatsApp function
        function openWhatsApp(service, defaultMessage) {
            const message = defaultMessage || `I am interested in ${service}. Please provide more information.`;
            const whatsappNumber = "{{ whatsapp_number }}";
            const encodedMessage = encodeURIComponent(message);
            const whatsappUrl = `https://wa.me/${whatsappNumber.replace('+', '')}?text=${encodedMessage}`;
            window.open(whatsappUrl, '_blank');
        }
    </script>
</body>
</html>
